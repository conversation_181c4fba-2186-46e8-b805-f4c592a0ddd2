import { NextRequest, NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import User from '../../models/users';
import bcrypt from 'bcryptjs';
import * as jose from 'jose';
import connectDB from '../../lib/mongodb';

connectDB();

export async function GET(request: NextRequest) {
  // 1. Using 'next/headers' helpers
  //const cookieStore = await cookies();
  //const token = cookieStore.get('token');

  /* const headersList = await headers();
  const referer = headersList.get('referer'); */

  // 2. Using the standard Web APIs
  //const userAgent = request.headers.get('user-agent');

  /* return new Response(JSON.stringify({ token, referer, userAgent }), {
    headers: { 'Content-Type': 'application/json' },
  }); */
  try {

    const body = await request.json();
    const { email, password } = body
    connectDB();

    return new NextResponse(JSON.stringify({
      name: "Hello",
      username: "username",
      email: "Email"
    }), {
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error("Error from fetching data", error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, username, password, name } = body;
    console.log(body);

    if (!email || !password || !name || !username) {
      return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
    }

    const hashedPassword = bcrypt.hashSync(password, 10);

    const newUser = new User({
      name,
      username,
      email,
      password: hashedPassword,
    });

    await newUser.save();

    // Create JWT token payload with string ID
    const tokenPayload = {
      id: (newUser._id as any).toString(),
      email: newUser.email,
      name: newUser.name,
    };

    // Generate token using jose
    const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET as string);
    const token = await new jose.SignJWT(tokenPayload)
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime('30d')
      .sign(JWT_SECRET);

    return NextResponse.json({ token }, { status: 201 });
  } catch (err) {
    console.error(err);
    return NextResponse.json({ error: 'Failed to save' }, { status: 500 })
  }
}