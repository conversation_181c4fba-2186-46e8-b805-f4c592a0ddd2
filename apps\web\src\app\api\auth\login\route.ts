import { NextRequest, NextResponse } from 'next/server';
import User from '../../../models/users';
import bcrypt from 'bcryptjs';
import connectDB from '../../../lib/mongodb';
import { userLoginSchema, validateInput, authRateLimiter, sanitizeInput } from '../../../lib/validation';
import {
  createAccessToken,
  createRefreshToken,
  hashRefreshToken,
  getAccessTokenCookieOptions,
  getRefreshTokenCookieOptions
} from '../../../lib/tokenUtils';

// Account lockout configuration
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes

export async function POST(request: NextRequest) {
    try {
        await connectDB();

        // Get client IP for rate limiting
        const clientIP = request.headers.get('x-forwarded-for') ||
                        request.headers.get('x-real-ip') ||
                        'unknown';

        // Check rate limiting
        const rateLimitResult = authRateLimiter.check(`login:${clientIP}`);
        if (!rateLimitResult.success) {
            return NextResponse.json({
                error: "Too many login attempts. Please try again later.",
                retryAfter: rateLimitResult.reset
            }, {
                status: 429,
                headers: {
                    'Retry-After': Math.ceil((rateLimitResult.reset.getTime() - Date.now()) / 1000).toString()
                }
            });
        }

        // Parse and validate request body
        const body = await request.json();

        // Sanitize inputs
        const sanitizedEmail = sanitizeInput(body.email || '');
        const sanitizedPassword = sanitizeInput(body.password || '');

        // Validate input using Zod schema
        const validation = validateInput(userLoginSchema, {
            email: sanitizedEmail,
            password: sanitizedPassword
        });

        if (!validation.success) {
            return NextResponse.json({
                error: "Invalid credentials"
            }, { status: 401 });
        }

        const { email, password } = validation.data;

        // Find the user (using timing-safe approach)
        const user = await User.findOne({ email }).select('+password +failedLoginAttempts +accountLockedUntil');

        // Always perform password comparison to prevent timing attacks
        const dummyHash = '$2a$12$dummy.hash.to.prevent.timing.attacks.dummy.hash.value';
        const passwordToCheck = user ? user.password : dummyHash;
        const passwordMatch = await bcrypt.compare(password, passwordToCheck);

        // Check if user exists and password matches
        if (!user || !passwordMatch) {
            // Increment rate limiting for failed attempts
            authRateLimiter.check(`login:${clientIP}`);

            return NextResponse.json({
                error: "Invalid credentials"
            }, { status: 401 });
        }

        // Check if account is locked
        if (user.accountLockedUntil && user.accountLockedUntil > new Date()) {
            return NextResponse.json({
                error: "Account temporarily locked due to too many failed attempts. Please try again later."
            }, { status: 423 });
        }

        // Reset failed login attempts on successful login
        if (user.failedLoginAttempts > 0) {
            user.failedLoginAttempts = 0;
            user.accountLockedUntil = null as any;
        }

        // Update last login time
        user.lastLoginAt = new Date();

        // Create tokens
        const accessTokenPayload = {
            id: (user._id as any).toString(),
            email: user.email,
            name: user.name,
            username: user.username,
        };

        const accessToken = await createAccessToken(accessTokenPayload);
        const { token: refreshToken, jti } = await createRefreshToken((user._id as any).toString());

        // Hash and store refresh token
        const hashedRefreshToken = await hashRefreshToken(refreshToken);
        user.refreshTokenHash = hashedRefreshToken;
        user.refreshTokenJTI = jti;

        await user.save();

        // Create response with secure cookies
        const response = NextResponse.json({
            message: 'Login successful',
            user: {
                id: (user._id as any).toString(),
                email: user.email,
                name: user.name,
                username: user.username
            }
        }, { status: 200 });

        // Set secure cookies
        const accessCookieOptions = getAccessTokenCookieOptions();
        const refreshCookieOptions = getRefreshTokenCookieOptions();

        response.cookies.set('accessToken', accessToken, accessCookieOptions);
        response.cookies.set('refreshToken', refreshToken, refreshCookieOptions);

        // Reset rate limiting on successful login
        authRateLimiter.reset(`login:${clientIP}`);

        return response;

    } catch (error) {
        console.error("Login API Error:", error);
        return NextResponse.json({
            error: 'An internal server error occurred'
        }, { status: 500 });
    }
}