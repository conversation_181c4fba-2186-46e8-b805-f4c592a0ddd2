'use client';

// Client-side authentication manager with automatic token refresh
export interface User {
  id: string;
  email: string;
  name: string;
  username: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Event types for auth state changes
export type AuthEvent = 'login' | 'logout' | 'token-refresh' | 'auth-error';

class AuthManager {
  private authState: AuthState = {
    user: null,
    isAuthenticated: false,
    isLoading: true
  };

  private listeners: Map<AuthEvent, Set<(data?: any) => void>> = new Map();
  private refreshPromise: Promise<boolean> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor() {
    // Initialize event listeners
    this.listeners.set('login', new Set());
    this.listeners.set('logout', new Set());
    this.listeners.set('token-refresh', new Set());
    this.listeners.set('auth-error', new Set());

    // Check initial auth state
    this.initializeAuth();
  }

  // Initialize authentication state
  private async initializeAuth() {
    try {
      const isValid = await this.validateCurrentSession();
      if (isValid) {
        this.scheduleTokenRefresh();
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
    } finally {
      this.authState.isLoading = false;
      this.notifyListeners('token-refresh');
    }
  }

  // Validate current session by making a test API call
  private async validateCurrentSession(): Promise<boolean> {
    try {
      const response = await this.makeAuthenticatedRequest('/api/auth/validate', {
        method: 'GET'
      });

      if (response.ok) {
        const data = await response.json();
        this.authState.user = data.user;
        this.authState.isAuthenticated = true;
        return true;
      }
    } catch (error) {
      console.error('Session validation failed:', error);
    }

    this.authState.user = null;
    this.authState.isAuthenticated = false;
    return false;
  }

  // Make authenticated API requests with automatic token refresh
  public async makeAuthenticatedRequest(url: string, options: RequestInit = {}): Promise<Response> {
    // First attempt
    let response = await fetch(url, {
      ...options,
      credentials: 'include', // Include cookies
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    // If unauthorized, try to refresh token
    if (response.status === 401) {
      const refreshed = await this.refreshTokens();
      
      if (refreshed) {
        // Retry the original request
        response = await fetch(url, {
          ...options,
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });
      } else {
        // Refresh failed, redirect to login
        this.handleAuthFailure();
        throw new Error('Authentication failed');
      }
    }

    return response;
  }

  // Refresh tokens
  private async refreshTokens(): Promise<boolean> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;

    return result;
  }

  private async performTokenRefresh(): Promise<boolean> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Schedule next refresh
        this.scheduleTokenRefresh();
        this.notifyListeners('token-refresh');
        return true;
      } else {
        console.error('Token refresh failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }

  // Schedule automatic token refresh (refresh 5 minutes before expiry)
  private scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Access tokens expire in 15 minutes, refresh after 10 minutes
    const refreshInterval = 10 * 60 * 1000; // 10 minutes

    this.refreshTimer = setTimeout(async () => {
      const success = await this.refreshTokens();
      if (!success) {
        this.handleAuthFailure();
      }
    }, refreshInterval);
  }

  // Handle authentication failure
  private handleAuthFailure() {
    this.authState.user = null;
    this.authState.isAuthenticated = false;
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    this.notifyListeners('auth-error');
    
    // Redirect to login page
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }

  // Login method
  public async login(email: string, password: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        this.authState.user = data.user;
        this.authState.isAuthenticated = true;
        this.scheduleTokenRefresh();
        this.notifyListeners('login', data.user);
        return { success: true };
      } else {
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  }

  // Logout method
  public async logout(): Promise<void> {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.authState.user = null;
      this.authState.isAuthenticated = false;
      
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }

      this.notifyListeners('logout');
      
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
  }

  // Event listener management
  public addEventListener(event: AuthEvent, callback: (data?: any) => void) {
    this.listeners.get(event)?.add(callback);
  }

  public removeEventListener(event: AuthEvent, callback: (data?: any) => void) {
    this.listeners.get(event)?.delete(callback);
  }

  private notifyListeners(event: AuthEvent, data?: any) {
    this.listeners.get(event)?.forEach(callback => callback(data));
  }

  // Getters
  public getAuthState(): AuthState {
    return { ...this.authState };
  }

  public isAuthenticated(): boolean {
    return this.authState.isAuthenticated;
  }

  public getUser(): User | null {
    return this.authState.user;
  }

  public isLoading(): boolean {
    return this.authState.isLoading;
  }
}

// Export singleton instance
export const authManager = new AuthManager();
