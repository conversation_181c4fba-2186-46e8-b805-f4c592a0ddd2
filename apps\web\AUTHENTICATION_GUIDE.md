# 🔐 Authentication System Guide

## Overview

This authentication system implements industry-standard security practices with automatic token refresh, similar to major platforms like Google, Facebook, and GitHub. The system is designed to be secure, user-friendly, and transparent.

## 🏗️ Architecture

### Token Strategy
- **Access Tokens**: Short-lived (15 minutes) for API requests
- **Refresh Tokens**: Long-lived (7 days) for obtaining new access tokens
- **Automatic Rotation**: Refresh tokens are rotated on each use for maximum security

### Security Features
- ✅ Cryptographically secure JWT secrets
- ✅ Strong password requirements (12+ chars with complexity)
- ✅ Rate limiting (5 attempts per 15 minutes)
- ✅ Secure cookie settings (httpOnly, secure, sameSite)
- ✅ Refresh token hashing in database
- ✅ Token rotation on refresh
- ✅ Account lockout protection
- ✅ Input validation and sanitization
- ✅ Generic error messages (prevents user enumeration)
- ✅ Security headers (XSS, CSRF, etc.)

## 🔄 How Automatic Token Refresh Works

### The Flow

1. **Initial Login**
   ```
   User logs in → Server creates access + refresh tokens → Tokens stored in secure cookies
   ```

2. **API Requests**
   ```
   Client makes request → Access token validated → Request processed
   ```

3. **Token Expiry**
   ```
   Access token expires → Client gets 401 → Automatic refresh triggered → New tokens issued → Original request retried
   ```

4. **Background Refresh**
   ```
   Timer runs every 10 minutes → Proactively refreshes tokens → User never sees interruption
   ```

### Behind the Scenes

The `AuthManager` class handles all token operations transparently:

```typescript
// Automatic request handling with token refresh
const response = await authManager.makeAuthenticatedRequest('/api/data');
// If token expired, it's automatically refreshed and request retried
```

### Key Components

1. **AuthManager** (`lib/authClient.ts`)
   - Manages authentication state
   - Handles automatic token refresh
   - Provides event system for state changes

2. **useAuth Hook** (`hooks/useAuth.ts`)
   - React hook for easy auth integration
   - Provides auth state and methods
   - Handles component re-renders

3. **Token Utilities** (`lib/tokenUtils.ts`)
   - JWT creation and verification
   - Secure token handling with Web Crypto API (Edge Runtime compatible)
   - Cookie management

## 📱 Usage Examples

### Basic Authentication

```tsx
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  if (!isAuthenticated) {
    return <LoginForm onLogin={login} />;
  }

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Making Authenticated Requests

```tsx
function DataComponent() {
  const { makeAuthenticatedRequest } = useAuth();
  const [data, setData] = useState(null);

  const fetchData = async () => {
    try {
      // This automatically handles token refresh if needed
      const response = await makeAuthenticatedRequest('/api/user-data');
      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Request failed:', error);
    }
  };

  return (
    <div>
      <button onClick={fetchData}>Load Data</button>
      {data && <pre>{JSON.stringify(data, null, 2)}</pre>}
    </div>
  );
}
```

### Protected Routes

```tsx
import { useRequireAuth } from '../hooks/useAuth';

function ProtectedPage() {
  const { isAuthenticated, isLoading } = useRequireAuth();

  if (isLoading) return <LoadingSpinner />;
  if (!isAuthenticated) return null; // Will redirect to login

  return <ProtectedContent />;
}
```

## 🛡️ Security Measures

### Password Security
- Minimum 12 characters
- Must contain: uppercase, lowercase, number, special character
- Hashed with bcrypt (12 rounds)
- Async hashing to prevent blocking

### Token Security
- Cryptographically secure secrets (64-byte random)
- Short access token lifetime (15 minutes)
- Refresh token rotation on each use
- Refresh tokens hashed in database
- JWT includes issuer, audience, and unique ID (jti)

### Cookie Security
```typescript
{
  httpOnly: true,           // Prevents XSS access
  secure: true,            // HTTPS only in production
  sameSite: 'strict',      // CSRF protection
  path: '/',               // Scope to entire app
  maxAge: 900              // 15 minutes for access token
}
```

### Rate Limiting
- 5 login attempts per 15 minutes per IP
- Account lockout after 5 failed attempts
- 30-minute lockout duration

### Input Validation
- Server-side validation with Zod schemas
- Input sanitization to prevent XSS
- Length limits on all fields
- Email format validation

## 🔧 Configuration

### Environment Variables
```env
# Strong, randomly generated secrets
JWT_SECRET="your-64-byte-random-secret-here"
REFRESH_TOKEN_SECRET="your-64-byte-random-secret-here"

# Rate limiting
RATE_LIMIT_WINDOW_MS="900000"  # 15 minutes
RATE_LIMIT_MAX_ATTEMPTS="5"   # 5 attempts
```

### Database Schema
```typescript
interface User {
  name: string;
  email: string;
  username: string;
  password: string;              // bcrypt hashed
  refreshTokenHash: string;      // hashed refresh token
  refreshTokenJTI: string;       // JWT ID for token tracking
  lastLoginAt: Date;
  failedLoginAttempts: number;
  accountLockedUntil: Date;
}
```

## 🚀 Deployment Checklist

- [ ] Generate secure JWT secrets
- [ ] Enable HTTPS in production
- [ ] Configure security headers
- [ ] Set up rate limiting (consider Redis for production)
- [ ] Configure CORS properly
- [ ] Set up monitoring and logging
- [ ] Test token refresh flow
- [ ] Verify cookie security settings

## 🔍 Monitoring

The system logs important security events:
- Failed login attempts
- Token refresh failures
- Rate limit violations
- Account lockouts

Consider implementing:
- Security event monitoring
- Failed login alerting
- Unusual activity detection
- Token usage analytics

## 🆘 Troubleshooting

### Common Issues

1. **Token Refresh Fails**
   - Check JWT secrets are set correctly
   - Verify refresh token exists in database
   - Check token expiry times

2. **Rate Limiting Too Aggressive**
   - Adjust `RATE_LIMIT_MAX_ATTEMPTS`
   - Increase `RATE_LIMIT_WINDOW_MS`
   - Consider IP whitelisting for development

3. **Cookies Not Set**
   - Verify HTTPS in production
   - Check `sameSite` settings
   - Ensure domain matches

4. **Authentication Loops**
   - Check middleware configuration
   - Verify token validation logic
   - Check for circular redirects

## 📚 API Reference

### Authentication Endpoints

- `POST /api/auth/login` - User login
- `POST /api/auth/signup` - User registration
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout
- `GET /api/auth/validate` - Session validation

### Response Formats

All endpoints return consistent JSON responses:

```typescript
// Success
{
  message: string;
  user?: User;
}

// Error
{
  error: string;
  details?: string[];
}
```

This authentication system provides enterprise-grade security while maintaining excellent user experience through seamless automatic token refresh.
