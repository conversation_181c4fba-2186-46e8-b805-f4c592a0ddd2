import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import User from '../../../models/users';
import connectDB from '../../../lib/mongodb';
import { verifyAccessToken } from '../../../lib/tokenUtils';

export async function GET(request: NextRequest) {
    try {
        await connectDB();

        // Get access token from cookies
        const cookieStore = await cookies();
        const accessToken = cookieStore.get('accessToken')?.value;

        if (!accessToken) {
            return NextResponse.json({ 
                error: 'No access token found' 
            }, { status: 401 });
        }

        // Verify access token
        const payload = await verifyAccessToken(accessToken);
        if (!payload || !payload.id) {
            return NextResponse.json({ 
                error: 'Invalid access token' 
            }, { status: 401 });
        }

        // Get user from database
        const user = await User.findById(payload.id).select('-password -refreshTokenHash');
        if (!user) {
            return NextResponse.json({ 
                error: 'User not found' 
            }, { status: 401 });
        }

        // Return user data
        return NextResponse.json({
            user: {
                id: user._id.toString(),
                email: user.email,
                name: user.name,
                username: user.username,
            }
        }, { status: 200 });

    } catch (error) {
        console.error('Session validation error:', error);
        return NextResponse.json({ 
            error: 'Session validation failed' 
        }, { status: 500 });
    }
}
