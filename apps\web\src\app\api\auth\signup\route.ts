import { NextRequest, NextResponse } from 'next/server';
import User from '../../../models/users';
import bcrypt from 'bcryptjs';
import connectDB from '../../../lib/mongodb';
import { userRegistrationSchema, validateInput, authRateLimiter, sanitizeInput } from '../../../lib/validation';
import {
  createAccessToken,
  createRefreshToken,
  hashRefreshToken,
  getAccessTokenCookieOptions,
  getRefreshTokenCookieOptions
} from '../../../lib/tokenUtils';

export async function POST(request: NextRequest) {
    try {
        await connectDB();

        // Get client IP for rate limiting
        const clientIP = request.headers.get('x-forwarded-for') ||
                        request.headers.get('x-real-ip') ||
                        'unknown';

        // Check rate limiting
        const rateLimitResult = authRateLimiter.check(`signup:${clientIP}`);
        if (!rateLimitResult.success) {
            return NextResponse.json({
                error: "Too many registration attempts. Please try again later.",
                retryAfter: rateLimitResult.reset
            }, {
                status: 429,
                headers: {
                    'Retry-After': Math.ceil((rateLimitResult.reset.getTime() - Date.now()) / 1000).toString()
                }
            });
        }

        // Parse and validate request body
        const body = await request.json();

        // Sanitize inputs
        const sanitizedData = {
            email: sanitizeInput(body.email || ''),
            username: sanitizeInput(body.username || ''),
            password: sanitizeInput(body.password || ''),
            name: sanitizeInput(body.name || '')
        };

        // Validate input using Zod schema
        const validation = validateInput(userRegistrationSchema, sanitizedData);

        if (!validation.success) {
            return NextResponse.json({
                error: "Invalid input data",
                details: validation.errors
            }, { status: 400 });
        }

        const { email, username, password, name } = validation.data;

        // Check for existing user (timing-safe approach)
        const existingUser = await User.findOne({
            $or: [{ email }, { username }]
        });

        if (existingUser) {
            // Generic error message to prevent user enumeration
            return NextResponse.json({
                error: 'An account with this email or username already exists.'
            }, { status: 409 });
        }

        // Hash password asynchronously
        const hashedPassword = await bcrypt.hash(password, 12);

        // Create new user
        const newUser = new User({
            name,
            username,
            email,
            password: hashedPassword,
            lastLoginAt: new Date(),
            failedLoginAttempts: 0
        });

        await newUser.save();

        // Create tokens
        const accessTokenPayload = {
            id: (newUser._id as any).toString(),
            email: newUser.email,
            name: newUser.name,
            username: newUser.username,
        };

        const accessToken = await createAccessToken(accessTokenPayload);
        const { token: refreshToken, jti } = await createRefreshToken((newUser._id as any).toString());

        // Hash and store refresh token
        const hashedRefreshToken = await hashRefreshToken(refreshToken);
        newUser.refreshTokenHash = hashedRefreshToken;
        newUser.refreshTokenJTI = jti;

        await newUser.save();

        // Create response with secure cookies
        const response = NextResponse.json({
            message: "Registration successful",
            user: {
                id: (newUser._id as any).toString(),
                email: newUser.email,
                name: newUser.name,
                username: newUser.username
            }
        }, { status: 201 });

        // Set secure cookies
        const accessCookieOptions = getAccessTokenCookieOptions();
        const refreshCookieOptions = getRefreshTokenCookieOptions();

        response.cookies.set('accessToken', accessToken, accessCookieOptions);
        response.cookies.set('refreshToken', refreshToken, refreshCookieOptions);

        // Reset rate limiting on successful registration
        authRateLimiter.reset(`signup:${clientIP}`);

        return response;

    } catch (err: any) {
        console.error('Registration error:', err);

        if (err.code === 11000) {
            // MongoDB duplicate key error
            return NextResponse.json({
                error: 'An account with this email or username already exists.'
            }, { status: 409 });
        }

        return NextResponse.json({
            error: 'An internal server error occurred.'
        }, { status: 500 });
    }
}
