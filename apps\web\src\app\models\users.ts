import mongoose, { Document, Model } from 'mongoose'

export interface user extends Document{
    name: string,
    email: string,
    username: string,
    password: string,
    refreshTokenHash: string,
    refreshTokenJTI: string,
    lastLoginAt: Date,
    failedLoginAttempts: number,
    accountLockedUntil: Date,
    createdAt: Date,
    updatedAt: Date
}

const userSchema = new mongoose.Schema<user>({
    name: { type: String, required: true, maxlength: 100 },
    username: { type: String, required: true, unique: true, maxlength: 30 },
    email: { type: String, required: true, unique: true, maxlength: 254 },
    password: { type: String, required: true, maxlength: 128 },
    refreshTokenHash: { type: String, required: false },
    refreshTokenJTI: { type: String, required: false },
    lastLoginAt: { type: Date, required: false },
    failedLoginAttempts: { type: Number, default: 0 },
    accountLockedUntil: { type: Date, required: false },
    createdAt: { type: Date, required: false },
    updatedAt: { type: Date, required: false }
}, {
    timestamps: true // This will add createdAt and updatedAt fields
})

const User: Model<user> = mongoose.models.User || mongoose.model<user>('User', userSchema);

export default User;