import { z } from 'zod';

// Strong password validation schema
export const passwordSchema = z.string()
  .min(12, "Password must be at least 12 characters long")
  .max(128, "Password must not exceed 128 characters")
  .regex(/^(?=.*[a-z])/, "Password must contain at least one lowercase letter")
  .regex(/^(?=.*[A-Z])/, "Password must contain at least one uppercase letter")
  .regex(/^(?=.*\d)/, "Password must contain at least one number")
  .regex(/^(?=.*[@$!%*?&])/, "Password must contain at least one special character (@$!%*?&)");

// Email validation schema
export const emailSchema = z.string()
  .email("Invalid email address")
  .max(254, "Email must not exceed 254 characters")
  .toLowerCase()
  .trim();

// Username validation schema
export const usernameSchema = z.string()
  .min(3, "Username must be at least 3 characters long")
  .max(30, "Username must not exceed 30 characters")
  .regex(/^[a-zA-Z0-9_-]+$/, "Username can only contain letters, numbers, underscores, and hyphens")
  .trim();

// Name validation schema
export const nameSchema = z.string()
  .min(1, "Name is required")
  .max(100, "Name must not exceed 100 characters")
  .regex(/^[a-zA-Z\s'-]+$/, "Name can only contain letters, spaces, apostrophes, and hyphens")
  .trim();

// User registration schema
export const userRegistrationSchema = z.object({
  name: nameSchema,
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
});

// User login schema
export const userLoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
});

// Validation helper function
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => err.message);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

// Sanitize input to prevent XSS
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
    .slice(0, 1000); // Limit length
}

// Rate limiting types
export interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: Date;
}

// Simple in-memory rate limiter (for production, use Redis)
class InMemoryRateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private windowMs: number;
  private maxAttempts: number;

  constructor(windowMs: number = 15 * 60 * 1000, maxAttempts: number = 5) {
    this.windowMs = windowMs;
    this.maxAttempts = maxAttempts;
  }

  check(identifier: string): RateLimitResult {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      // First attempt or window expired
      this.attempts.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return {
        success: true,
        limit: this.maxAttempts,
        remaining: this.maxAttempts - 1,
        reset: new Date(now + this.windowMs)
      };
    }

    if (record.count >= this.maxAttempts) {
      // Rate limit exceeded
      return {
        success: false,
        limit: this.maxAttempts,
        remaining: 0,
        reset: new Date(record.resetTime)
      };
    }

    // Increment attempt count
    record.count++;
    this.attempts.set(identifier, record);

    return {
      success: true,
      limit: this.maxAttempts,
      remaining: this.maxAttempts - record.count,
      reset: new Date(record.resetTime)
    };
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Export rate limiter instance
export const authRateLimiter = new InMemoryRateLimiter(
  parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  parseInt(process.env.RATE_LIMIT_MAX_ATTEMPTS || '5')
);
