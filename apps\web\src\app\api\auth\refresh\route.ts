import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import User from '../../../models/users';
import connectDB from '../../../lib/mongodb';
import {
  verifyRefreshToken,
  createAccessToken,
  createRefreshToken,
  hashRefreshToken,
  verifyHashedRefreshToken,
  getAccessTokenCookieOptions,
  getRefreshTokenCookieOptions
} from '../../../lib/tokenUtils';

export async function POST(request: NextRequest) {
    try {
        await connectDB();

        // Get refresh token from cookies
        const cookieStore = await cookies();
        const refreshToken = cookieStore.get('refreshToken')?.value;

        if (!refreshToken) {
            return NextResponse.json({
                error: 'Refresh token not found'
            }, { status: 401 });
        }

        // Verify refresh token
        const payload = await verifyRefreshToken(refreshToken);
        if (!payload || !payload.id || !payload.jti) {
            return NextResponse.json({
                error: 'Invalid refresh token'
            }, { status: 401 });
        }

        // Find user and verify refresh token
        const user = await User.findById(payload.id);
        if (!user) {
            return NextResponse.json({
                error: 'User not found'
            }, { status: 401 });
        }

        // Verify stored refresh token hash
        if (!user.refreshTokenHash || !user.refreshTokenJTI) {
            return NextResponse.json({
                error: 'Invalid refresh token'
            }, { status: 401 });
        }

        // Check if JTI matches
        if (user.refreshTokenJTI !== payload.jti) {
            return NextResponse.json({
                error: 'Refresh token revoked'
            }, { status: 401 });
        }

        // Verify refresh token hash
        const isValidRefreshToken = await verifyHashedRefreshToken(refreshToken, user.refreshTokenHash);
        if (!isValidRefreshToken) {
            return NextResponse.json({
                error: 'Invalid refresh token'
            }, { status: 401 });
        }

        // Create new access token
        const accessTokenPayload = {
            id: (user._id as any).toString(),
            email: user.email,
            name: user.name,
            username: user.username,
        };

        const newAccessToken = await createAccessToken(accessTokenPayload);

        // Optionally rotate refresh token (recommended for high security)
        const { token: newRefreshToken, jti: newJTI } = await createRefreshToken((user._id as any).toString());
        const hashedNewRefreshToken = await hashRefreshToken(newRefreshToken);

        // Update user with new refresh token
        user.refreshTokenHash = hashedNewRefreshToken;
        user.refreshTokenJTI = newJTI;
        await user.save();

        // Create response
        const response = NextResponse.json({
            message: 'Tokens refreshed successfully'
        }, { status: 200 });

        // Set new secure cookies
        const accessCookieOptions = getAccessTokenCookieOptions();
        const refreshCookieOptions = getRefreshTokenCookieOptions();

        response.cookies.set('accessToken', newAccessToken, accessCookieOptions);
        response.cookies.set('refreshToken', newRefreshToken, refreshCookieOptions);

        return response;

    } catch (error) {
        console.error('Token refresh error:', error);
        return NextResponse.json({
            error: 'Token refresh failed'
        }, { status: 500 });
    }
}

// Support GET method for backward compatibility
export async function GET(request: NextRequest) {
    return POST(request);
}