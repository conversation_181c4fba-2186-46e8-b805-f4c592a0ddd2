'use client'

import React from 'react';

// --- Icon Imports (using lucide-react for a clean look) ---
import {
  Home,
  Compass,
  Bell,
  MessageSquare,
  User,
  Settings,
  Heart,
  MessageCircle,
  Repeat
} from 'lucide-react';
import { fail } from 'assert';

// --- Mock Data ---
const feedItems = [
  {
    id: 'post1',
    user: {
      name: '<PERSON>',
      handle: 'elenavoyage',
      avatar: 'https://placehold.co/48x48/f3f4f6/111827?text=E',
    },
    content: 'Just witnessed the most breathtaking sunrise from a hot air balloon over Cappadocia. The world is full of wonders. 🎈',
    timestamp: '3h ago',
  },
  {
    id: 'post2',
    user: {
      name: '<PERSON>',
      handle: 'davidcodes',
      avatar: 'https://placehold.co/48x48/f3f4f6/111827?text=D',
    },
    content: 'Deep diving into WebAssembly for a new side project. The potential for near-native performance on the web is mind-blowing.',
    timestamp: '8h ago',
  },
  {
    id: 'post3',
    user: {
      name: '<PERSON><PERSON>',
      handle: 'aishacreates',
      avatar: 'https://placehold.co/48x48/f3f4f6/111827?text=A',
    },
    content: 'Spent the weekend sketching in the park. Sometimes, stepping away from the screen and returning to analog tools is the best way to find creative clarity.',
    timestamp: '1d ago',
  },
];


// --- Dashboard Components ---

const Sidebar = () => {
  const navItems = [
    { icon: Home, label: 'Home' },
    { icon: Compass, label: 'Discover' },
    { icon: Bell, label: 'Notifications' },
    { icon: MessageSquare, label: 'Messages' },
    { icon: User, label: 'Profile' },
    { icon: Settings, label: 'Settings' },
  ];

  return (
    // Static sidebar, no animations or expansion
    <aside className="w-64 h-screen sticky top-0 flex flex-col p-4 bg-gray-900 border-r border-gray-800">
      {/* Logo Placeholder */}
      <div className="w-8 h-8 bg-gray-700 rounded-lg mb-10"></div>

      {/* Navigation */}
      <nav className="flex flex-col gap-4">
        {navItems.map((item, index) => (
          <a key={index} href="#" className="flex items-center gap-4 p-2 text-gray-300 rounded-md">
            <item.icon className="w-6 h-6" />
            <span className="text-lg font-medium">{item.label}</span>
          </a>
        ))}
      </nav>
    </aside>
  );
};

const PostCard = ({ item }) => (
  // A simple article card for each post
  <article className="p-4 border-b border-gray-800 flex space-x-4">
    <img src={item.user.avatar} alt={`${item.user.name}'s avatar`} className="w-12 h-12 rounded-full bg-gray-700 flex-shrink-0" />
    <div className="flex-1">
      <div className="flex items-baseline gap-2">
        <p className="font-bold text-white">{item.user.name}</p>
        <p className="text-gray-500 text-sm">@{item.user.handle}</p>
        <span className="text-gray-600">&middot;</span>
        <p className="text-gray-500 text-sm">{item.timestamp}</p>
      </div>
      <p className="text-gray-300 mt-1">{item.content}</p>
      {/* Simple action buttons without complex styles */}
      <div className="flex items-center justify-start gap-10 mt-4 text-gray-500">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
        </div>
        <div className="flex items-center gap-2">
          <Repeat className="w-5 h-5" />
        </div>
        <div className="flex items-center gap-2">
          <Heart className="w-5 h-5" />
        </div>
      </div>
    </div>
  </article>
);

const MainFeed = () => (
  <main className="w-full max-w-2xl">
    {/* Simple header */}
    <header className="sticky top-0 p-4 bg-gray-950/80 backdrop-blur-sm border-b border-gray-800">
      <h1 className="text-xl font-bold text-white">Home</h1>
    </header>

    {/* Post feed */}
    <div>
      {feedItems.map(item => (
        <PostCard key={item.id} item={item} />
      ))}
    </div>
  </main>
);

// --- Main App Component ---
export default function App() {
  return (
    <div className="bg-gray-950 min-h-screen text-white">
      <div className="flex justify-center">
        <Sidebar />
        <MainFeed />
      </div>
    </div>
  );
}
