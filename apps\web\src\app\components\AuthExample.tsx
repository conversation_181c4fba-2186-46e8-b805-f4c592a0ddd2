'use client';

import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';

// Example component showing how to use the authentication system
export function AuthExample() {
  const { user, isAuthenticated, isLoading, logout, makeAuthenticatedRequest } = useAuth();
  const [apiData, setApiData] = useState<any>(null);
  const [apiLoading, setApiLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  // Example of making an authenticated API request
  const fetchProtectedData = async () => {
    setApiLoading(true);
    setApiError(null);
    
    try {
      const response = await makeAuthenticatedRequest('/api/protected-endpoint');
      
      if (response.ok) {
        const data = await response.json();
        setApiData(data);
      } else {
        setApiError('Failed to fetch data');
      }
    } catch (error) {
      setApiError('Network error occurred');
      console.error('API request failed:', error);
    } finally {
      setApiLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">Please log in to access this content.</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Welcome, {user?.name}!</h2>
      
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">User Information</h3>
        <p><strong>ID:</strong> {user?.id}</p>
        <p><strong>Email:</strong> {user?.email}</p>
        <p><strong>Username:</strong> {user?.username}</p>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">API Request Example</h3>
        <button
          onClick={fetchProtectedData}
          disabled={apiLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {apiLoading ? 'Loading...' : 'Fetch Protected Data'}
        </button>
        
        {apiError && (
          <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
            Error: {apiError}
          </div>
        )}
        
        {apiData && (
          <div className="mt-2 p-2 bg-green-100 text-green-700 rounded">
            <pre>{JSON.stringify(apiData, null, 2)}</pre>
          </div>
        )}
      </div>

      <button
        onClick={handleLogout}
        className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
      >
        Logout
      </button>
    </div>
  );
}

// Example of a protected page component
export function ProtectedPage() {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please log in to access this page.</p>
          <a 
            href="/login" 
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  // Render protected content
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <AuthExample />
      </div>
    </div>
  );
}
