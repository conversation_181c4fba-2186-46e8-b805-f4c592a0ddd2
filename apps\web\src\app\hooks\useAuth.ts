'use client';

import { useState, useEffect, useCallback } from 'react';
import { authManager, AuthState, User } from '../lib/authClient';

export interface UseAuthReturn {
  // Auth state
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Auth actions
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  
  // Utility methods
  makeAuthenticatedRequest: (url: string, options?: RequestInit) => Promise<Response>;
}

export function useAuth(): UseAuthReturn {
  const [authState, setAuthState] = useState<AuthState>(authManager.getAuthState());

  // Update auth state when it changes
  useEffect(() => {
    const handleAuthChange = () => {
      setAuthState(authManager.getAuthState());
    };

    // Listen to all auth events
    authManager.addEventListener('login', handleAuthChange);
    authManager.addEventListener('logout', handleAuthChange);
    authManager.addEventListener('token-refresh', handleAuthChange);
    authManager.addEventListener('auth-error', handleAuthChange);

    // Initial state sync
    handleAuthChange();

    // Cleanup listeners
    return () => {
      authManager.removeEventListener('login', handleAuthChange);
      authManager.removeEventListener('logout', handleAuthChange);
      authManager.removeEventListener('token-refresh', handleAuthChange);
      authManager.removeEventListener('auth-error', handleAuthChange);
    };
  }, []);

  // Memoized login function
  const login = useCallback(async (email: string, password: string) => {
    return authManager.login(email, password);
  }, []);

  // Memoized logout function
  const logout = useCallback(async () => {
    return authManager.logout();
  }, []);

  // Memoized authenticated request function
  const makeAuthenticatedRequest = useCallback(async (url: string, options?: RequestInit) => {
    return authManager.makeAuthenticatedRequest(url, options);
  }, []);

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    login,
    logout,
    makeAuthenticatedRequest,
  };
}

// Hook for protecting routes
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login if not authenticated
      window.location.href = '/login';
    }
  }, [isAuthenticated, isLoading]);

  return { isAuthenticated, isLoading };
}

// Hook for redirecting authenticated users
export function useRedirectIfAuthenticated(redirectTo: string = '/dashboard') {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      window.location.href = redirectTo;
    }
  }, [isAuthenticated, isLoading, redirectTo]);

  return { isAuthenticated, isLoading };
}
