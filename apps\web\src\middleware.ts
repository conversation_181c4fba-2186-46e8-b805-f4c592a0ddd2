import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from './app/lib/tokenUtils';

export async function middleware(request: NextRequest) {
    const LOGIN_URI = new URL('/login', request.url);
    const cookieStore = await cookies();

    // Get access token from cookies
    const accessToken = cookieStore.get('accessToken')?.value;

    if (!accessToken) {
        return NextResponse.redirect(LOGIN_URI);
    }

    try {
        // Verify access token
        const payload = await verifyAccessToken(accessToken);

        if (!payload || !payload.id) {
            return NextResponse.redirect(LOGIN_URI);
        }

        // Add user information to request headers
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set("x-user-id", payload.id);
        requestHeaders.set("x-user-email", payload.email);
        requestHeaders.set("x-user-name", payload.name);

        // Continue to the next middleware or page
        return NextResponse.next({
            request: {
                headers: requestHeaders,
            },
        });

    } catch (error) {
        console.error("Middleware token verification failed:", error);
        return NextResponse.redirect(LOGIN_URI);
    }
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes - auth is handled separately)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - login (the login page itself)
         * - signup (the signup page)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|login|signup).*)'
    ],
}