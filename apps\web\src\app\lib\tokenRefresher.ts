
let accessToken = ''; // This will be stored in memory

// Function to set the token after login
export const setAccessToken = (token) => {
    accessToken = token;
};

// The core API function that handles token refresh
const api = async (url, options = {}) => {
    // Add the current access token to the headers
    options.headers = {
        ...options.headers,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
    };

    let response = await fetch(url, options);

    // If the token expired (401), try to refresh it
    if (response.status === 401) {
        try {
            // Call your refresh endpoint
            const refreshResponse = await fetch('/api/auth/refresh', { method: 'POST' });
            const { accessToken: newAccessToken } = await refreshResponse.json();

            if (!newAccessToken) throw new Error('Could not refresh token');

            // Update the in-memory access token
            setAccessToken(newAccessToken);

            // Retry the original request with the new token
            options.headers['Authorization'] = `Bearer ${newAccessToken}`;
            response = await fetch(url, options);

        } catch (refreshError) {
            // If refresh fails, redirect to login
            console.error("Session expired. Please log in again.");
            // window.location.href = '/login'; 
            return Promise.reject(refreshError);
        }
    }

    return response;
};

export default api;