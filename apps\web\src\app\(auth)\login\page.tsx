'use client'
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth, useRedirectIfAuthenticated } from "../../hooks/useAuth";

function LoginPage() {
    const router = useRouter();
    const { login, isLoading: authLoading } = useAuth();

    // Redirect if already authenticated
    useRedirectIfAuthenticated('/dashboard');

    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        remember: false
    });

    async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        setIsLoading(true);
        setMessage('');
        setError('');

        const form = new FormData(e.currentTarget);
        const formDataObj = Object.fromEntries(form);

        try {
            const result = await login(
                formDataObj.email as string,
                formDataObj.password as string
            );

            if (result.success) {
                setMessage('Login successful! Redirecting...');
                // The useRedirectIfAuthenticated hook will handle the redirect
                setTimeout(() => {
                    router.push('/dashboard');
                }, 1000);
            } else {
                setError(result.error || 'Login failed');
            }
        } catch (error) {
            console.error("Login error:", error);
            setError('Network error. Please try again.');
        } finally {
            setIsLoading(false);
        }
    }


    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
            <div className="bg-gray-800 border-2 border-gray-600 shadow-lg rounded-none p-8 w-full max-w-md">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-white mb-2">
                        Login
                    </h1>
                    <p className="text-gray-400 text-sm">Enter your credentials</p>
                </div>
                <form onSubmit={handleSubmit}>
                    {/* Error Message */}
                    {error && (
                        <div className="mb-4 p-3 bg-red-900 border border-red-700 rounded text-red-200 text-sm">
                            {error}
                        </div>
                    )}

                    {/* Success Message */}
                    {message && (
                        <div className="mb-4 p-3 bg-green-900 border border-green-700 rounded text-green-200 text-sm">
                            {message}
                        </div>
                    )}

                    <div className="space-y-6">
                        {/* Email Input */}
                        <div className="relative">
                            <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                                Email
                            </label>
                            <div className="relative">
                                {/* Replace with your icon if needed */}
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5">
                                    {/* Mail Icon */}

                                </span>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    className="w-full pl-4 pr-4 py-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors"
                                />
                            </div>
                        </div>

                        {/* Password Input */}
                        <div className="relative">
                            <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                                Password
                            </label>
                            <div className="relative">
                                {/* Lock Icon */}
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5">

                                </span>
                                <input
                                    id="password"
                                    name="password"
                                    type="password"
                                    placeholder="********"
                                    className="w-full pl-4 pr-12 py-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors"
                                />
                                {/* Eye Icon (static, not functional) */}
                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5 cursor-pointer">
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z" /><circle cx="12" cy="12" r="3" /></svg>
                                </span>
                            </div>
                        </div>

                        {/* Remember Me & Forgot Password */}
                        <div className="flex items-center justify-between text-sm">
                            <label className="flex items-center text-gray-300">
                                <input type="checkbox" className="mr-2 w-4 h-4 border-2 border-gray-600 rounded-none bg-gray-700" />
                                <input type="hidden" name="remember" value="true" />
                                Remember me
                            </label>
                            <a href="#" className="text-white hover:text-gray-300 underline transition-colors">
                                Forgot password?
                            </a>
                        </div>

                        {/* Login Button */}
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full bg-white text-black font-semibold py-3 rounded-none border-2 border-white hover:bg-gray-800 hover:text-white transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? 'Logging in...' : 'Login'}
                        </button>

                        {/* Divider */}
                        <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                                <div className="w-full border-t border-gray-600"></div>
                            </div>
                            <div className="relative flex justify-center text-sm">
                                <span className="px-2 bg-gray-800 text-gray-400">Or</span>
                            </div>
                        </div>

                        {/* Social Login */}
                        <div className="grid grid-cols-2 gap-3">
                            <button
                                type="button"
                                className="flex items-center justify-center px-4 py-2 border-2 border-gray-600 rounded-none bg-gray-800 text-white hover:bg-white hover:text-black transition-all duration-200"
                            >
                                {/* Google Icon */}
                                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                </svg>
                                Google
                            </button>
                            <button
                                type="button"
                                className="flex items-center justify-center px-4 py-2 border-2 border-gray-600 rounded-none bg-gray-800 text-white hover:bg-white hover:text-black transition-all duration-200"
                            >
                                {/* GitHub Icon */}
                                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                                </svg>
                                GitHub
                            </button>
                        </div>
                    </div>

                    {/* Sign Up Link */}
                    <p className="mt-8 text-center text-sm text-gray-400">
                        Don't have an account?{' '}
                        <a href="/signup" className="text-white font-medium underline hover:text-gray-300 transition-colors">
                            Sign up
                        </a>
                    </p>
                </form>
            </div>
        </div>
    );
}

export default LoginPage;