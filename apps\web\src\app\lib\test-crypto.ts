// Test file to verify Web Crypto API implementation works
// This can be deleted after testing

export function testWebCrypto() {
  try {
    // Test the generateJTI function equivalent
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    const jti = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    
    console.log('Generated JTI:', jti);
    console.log('JTI length:', jti.length);
    console.log('Web Crypto API working correctly!');
    
    return jti;
  } catch (error) {
    console.error('Web Crypto API test failed:', error);
    throw error;
  }
}

// Test that we can generate multiple unique JTIs
export function testUniqueJTIs() {
  const jtis = new Set();
  
  for (let i = 0; i < 100; i++) {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    const jti = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    jtis.add(jti);
  }
  
  console.log(`Generated ${jtis.size} unique JTIs out of 100 attempts`);
  return jtis.size === 100; // Should be true if all are unique
}
