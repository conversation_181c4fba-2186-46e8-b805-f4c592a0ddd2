import { NextRequest, NextResponse } from "next/server";
import { cookies } from 'next/headers';
import User from '../../../models/users';
import connectDB from '../../../lib/mongodb';
import { verifyRefreshToken, getClearCookieOptions } from '../../../lib/tokenUtils';

export async function POST() {
    try {
        await connectDB();

        // Get refresh token from cookies
        const cookieStore = await cookies();
        const refreshToken = cookieStore.get('refreshToken')?.value;

        if (refreshToken) {
            // Verify and invalidate refresh token
            const payload = await verifyRefreshToken(refreshToken);
            if (payload && payload.id) {
                // Clear refresh token from database
                await User.findByIdAndUpdate(payload.id, {
                    $unset: {
                        refreshTokenHash: 1,
                        refreshTokenJTI: 1
                    }
                });
            }
        }

        // Create response
        const response = NextResponse.json({
            message: "Logout successful"
        }, { status: 200 });

        // Clear both cookies with secure options
        const clearOptions = getClearCookieOptions();

        response.cookies.set('accessToken', '', clearOptions);
        response.cookies.set('refreshToken', '', clearOptions);

        return response;

    } catch (error) {
        console.error('Logout error:', error);

        // Even if there's an error, clear the cookies
        const response = NextResponse.json({
            message: "Logout completed"
        }, { status: 200 });

        const clearOptions = getClearCookieOptions();
        response.cookies.set('accessToken', '', clearOptions);
        response.cookies.set('refreshToken', '', clearOptions);

        return response;
    }
}

// Support GET method for backward compatibility
export async function GET() {
    return POST();
}